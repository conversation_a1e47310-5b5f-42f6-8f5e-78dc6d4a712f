const webpack = require('webpack');

module.exports = {
  plugins: [
    new webpack.DefinePlugin({
      global: 'globalThis',
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10,
          reuseExistingChunk: true,
        },
        angular: {
          test: /[\\/]node_modules[\\/]@angular[\\/]/,
          name: 'angular',
          chunks: 'all',
          priority: 20,
          reuseExistingChunk: true,
        },
        translate: {
          test: /[\\/]node_modules[\\/]@ngx-translate[\\/]/,
          name: 'translate',
          chunks: 'all',
          priority: 15,
          reuseExistingChunk: true,
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5,
          reuseExistingChunk: true,
        },
      },
    },
    runtimeChunk: 'single',
  },
};
