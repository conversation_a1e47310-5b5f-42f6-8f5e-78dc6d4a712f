<!-- Order Tracking Component -->
<div class="order-tracking-container">
  <!-- Header -->
  <div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900 mb-2">
      {{ 'admin.order_tracking.title' | translate }}
    </h2>
    <p class="text-gray-600">
      {{ 'admin.order_tracking.all_orders_from_tenants' | translate }}
    </p>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ 'search' | translate }}
        </label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (keyup.enter)="onSearch()"
          [placeholder]="'admin.order_tracking.search_placeholder' | translate"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
      </div>

      <!-- Domain Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ 'admin.order_tracking.filter_by_domain' | translate }}
        </label>
        <select
          [(ngModel)]="selectedTenantId"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <option value="">All Domains</option>
          <option *ngFor="let tenant of tenants" [value]="tenant.id">
            {{ tenant.domain }}
          </option>
        </select>
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ 'admin.order_tracking.status' | translate }}
        </label>
        <select
          [(ngModel)]="selectedStatus"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <option *ngFor="let status of orderStatuses" [value]="status.value">
            {{ status.label }}
          </option>
        </select>
      </div>

      <!-- Date Range -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ 'admin.order_tracking.filter_by_date' | translate }}
        </label>
        <div class="flex space-x-2">
          <input
            type="date"
            [(ngModel)]="startDate"
            (change)="onFilterChange()"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <input
            type="date"
            [(ngModel)]="endDate"
            (change)="onFilterChange()"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
      </div>
    </div>

    <!-- Filter Actions -->
    <div class="flex justify-between items-center">
      <button
        (click)="onSearch()"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        <i class="fas fa-search mr-2"></i>
        {{ 'admin.order_tracking.apply_filter' | translate }}
      </button>
      
      <button
        (click)="clearFilters()"
        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
        <i class="fas fa-times mr-2"></i>
        {{ 'admin.order_tracking.clear_filter' | translate }}
      </button>
    </div>
  </div>

  <!-- Orders Table -->
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">{{ 'admin.order_tracking.loading' | translate }}</span>
    </div>

    <!-- Orders Table -->
    <div *ngIf="!isLoading" class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.order_id' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.domain' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.user' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.service' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.amount' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.status' | translate }}
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ 'admin.order_tracking.created_date' | translate }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let order of orders" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ getDomainName(order.tenant_id) }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ order.user_name }}</div>
              <div class="text-sm text-gray-500">{{ order.user_email }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ order.service_name }}</div>
              <div class="text-sm text-gray-500">{{ order.category_name }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">
                {{ formatCurrency(order.actual_charge || order.charge, order.currency) }}
              </div>
              <div class="text-sm text-gray-500">Qty: {{ order.quantity }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="getStatusClass(order.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ order.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ formatDate(order.created_at) }}
            </td>
          </tr>
          
          <!-- No Orders Found -->
          <tr *ngIf="orders.length === 0">
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              {{ 'admin.order_tracking.no_orders_found' | translate }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="!isLoading && orders.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div class="flex-1 flex justify-between sm:hidden">
        <button
          [disabled]="pagination.page_number === 0"
          (click)="onPageChange(pagination.page_number - 1)"
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          {{ 'admin.users.previous' | translate }}
        </button>
        <button
          [disabled]="pagination.page_number >= pagination.total_pages - 1"
          (click)="onPageChange(pagination.page_number + 1)"
          class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          {{ 'admin.users.next' | translate }}
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            {{ 'admin.users.showing' | translate }}
            <span class="font-medium">{{ pagination.page_number * pagination.page_size + 1 }}</span>
            {{ 'admin.users.to' | translate }}
            <span class="font-medium">{{ Math.min((pagination.page_number + 1) * pagination.page_size, pagination.total_elements) }}</span>
            {{ 'admin.users.of' | translate }}
            <span class="font-medium">{{ pagination.total_elements }}</span>
            {{ 'admin.users.results' | translate }}
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button
              [disabled]="pagination.page_number === 0"
              (click)="onPageChange(pagination.page_number - 1)"
              class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button
              [disabled]="pagination.page_number >= pagination.total_pages - 1"
              (click)="onPageChange(pagination.page_number + 1)"
              class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
              <i class="fas fa-chevron-right"></i>
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
